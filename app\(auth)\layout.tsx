export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Subtle decorative background elements */}
      <div className="absolute inset-0 opacity-5">
        {/* Horizontal grid lines */}
        <div className="absolute top-1/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/60 to-transparent"></div>
        <div className="absolute top-2/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/50 to-transparent"></div>
        <div className="absolute top-3/4 left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/40 to-transparent"></div>

        {/* Vertical grid lines */}
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-orange-500/60 to-transparent"></div>
        <div className="absolute top-0 left-2/4 w-px h-full bg-gradient-to-b from-transparent via-red-500/50 to-transparent"></div>
        <div className="absolute top-0 left-3/4 w-px h-full bg-gradient-to-b from-transparent via-orange-400/40 to-transparent"></div>
      </div>

      {/* Background orbs */}
      <div className="absolute top-1/6 left-1/8 w-96 h-96 bg-gradient-to-br from-orange-500/8 to-red-600/5 rounded-full blur-3xl opacity-60 animate-pulse-slow"></div>
      <div className="absolute bottom-1/6 right-1/8 w-80 h-80 bg-gradient-to-br from-red-400/6 to-orange-500/4 rounded-full blur-3xl opacity-50 animate-float"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[60rem] h-[60rem] bg-gradient-to-br from-orange-500/3 to-red-500/2 rounded-full blur-3xl opacity-30"></div>

      {/* Main content - properly centered */}
      <div className="relative z-10 flex items-center justify-center min-h-screen px-6 pt-20">
        <div className="w-full max-w-md">
          {children}
        </div>
      </div>
    </div>
  );
}
