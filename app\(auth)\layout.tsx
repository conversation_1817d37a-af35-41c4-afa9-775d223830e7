export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Refined Grid System - Equal Spacing (exact match to homepage) */}
      <div className="absolute inset-0 opacity-[0.03]">
        {/* Horizontal grid lines - Equal 1/6 spacing */}
        <div className="absolute top-[16.666%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/80 to-transparent"></div>
        <div className="absolute top-[33.333%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-400/70 to-transparent"></div>
        <div className="absolute top-[50%] left-0 w-full h-px bg-gradient-to-r from-transparent via-red-500/80 to-transparent"></div>
        <div className="absolute top-[66.666%] left-0 w-full h-px bg-gradient-to-r from-transparent via-orange-500/70 to-transparent"></div>
        <div className="absolute top-[83.333%] left-0 w-full h-px bg-gradient-to-r from-transparent via-red-400/70 to-transparent"></div>

        {/* Vertical grid lines - Equal 1/6 spacing */}
        <div className="absolute top-0 left-[16.666%] w-px h-full bg-gradient-to-b from-transparent via-orange-500/80 to-transparent"></div>
        <div className="absolute top-0 left-[33.333%] w-px h-full bg-gradient-to-b from-transparent via-orange-400/70 to-transparent"></div>
        <div className="absolute top-0 left-[50%] w-px h-full bg-gradient-to-b from-transparent via-red-500/80 to-transparent"></div>
        <div className="absolute top-0 left-[66.666%] w-px h-full bg-gradient-to-b from-transparent via-orange-500/70 to-transparent"></div>
        <div className="absolute top-0 left-[83.333%] w-px h-full bg-gradient-to-b from-transparent via-red-400/70 to-transparent"></div>
      </div>

      {/* Large Background Orbs for Visual Flow (exact match to homepage) */}
      <div className="absolute top-1/4 left-1/8 w-80 h-80 bg-gradient-to-br from-orange-500/8 to-red-600/6 rounded-full blur-3xl opacity-60 animate-float"></div>
      <div className="absolute bottom-1/4 right-1/8 w-96 h-96 bg-gradient-to-br from-orange-400/6 to-yellow-500/4 rounded-full blur-3xl opacity-50 animate-float-delayed"></div>

      {/* Central Connecting Orb */}
      <div className="absolute top-3/4 left-1/2 transform -translate-x-1/2 w-[40rem] h-[40rem] bg-gradient-to-br from-orange-500/5 to-red-500/3 rounded-full blur-3xl opacity-40 animate-pulse-slow"></div>

      {/* Refined Accent Elements */}
      <div className="absolute top-1/6 right-1/4 w-32 h-32 bg-gradient-to-br from-orange-400/12 to-red-400/8 rounded-full blur-2xl opacity-60 animate-float-slow"></div>
      <div className="absolute bottom-1/6 left-1/4 w-40 h-40 bg-gradient-to-br from-red-400/10 to-orange-500/6 rounded-full blur-2xl opacity-50 animate-float-reverse"></div>

      {/* Main content - perfect center alignment with navbar consideration */}
      <div className="relative z-20 flex items-center justify-center min-h-[calc(100vh-64px)] mt-16 px-6">
        <div className="w-full max-w-md">
          {children}
        </div>
      </div>
    </div>
  );
}
