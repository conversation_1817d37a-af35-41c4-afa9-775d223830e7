import { Card, CardBody, CardHeader } from "@heroui/card";

interface AuthCardProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
}

export function AuthCard({ title, subtitle, children }: AuthCardProps) {
  return (
    <Card className="bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-xl border border-gray-700/50 shadow-2xl shadow-orange-500/10">
      <CardHeader className="flex flex-col gap-3 pb-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-2">{title}</h1>
          {subtitle && (
            <p className="text-gray-400 text-sm leading-relaxed">{subtitle}</p>
          )}
        </div>
      </CardHeader>
      <CardBody className="pt-0">
        {children}
      </CardBody>
    </Card>
  );
}
