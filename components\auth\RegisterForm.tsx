"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Link } from "@heroui/link";
import { EyeIcon, EyeSlashIcon } from "@/components/icons";
import { registerSchema, type RegisterFormData } from "@/lib/validations";

interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
}

export function RegisterForm({ onSubmit, isLoading = false, error }: RegisterFormProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isConfirmVisible, setIsConfirmVisible] = useState(false);
  
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<RegisterFormData>({
    resolver: zodResolver(registerSchema),
  });

  const toggleVisibility = () => setIsVisible(!isVisible);
  const toggleConfirmVisibility = () => setIsConfirmVisible(!isConfirmVisible);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="p-4 rounded-lg bg-red-500/10 border border-red-500/20 text-red-400 text-sm">
          {error}
        </div>
      )}

      <div className="space-y-4">
        <Input
          {...register("name")}
          type="text"
          label="Full Name"
          placeholder="Enter your full name"
          variant="bordered"
          isInvalid={!!errors.name}
          errorMessage={errors.name?.message}
          classNames={{
            input: "text-white",
            inputWrapper: "border-gray-600 hover:border-orange-500/50 focus-within:border-orange-500",
            label: "text-gray-300",
          }}
        />

        <Input
          {...register("email")}
          type="email"
          label="Email"
          placeholder="Enter your email"
          variant="bordered"
          isInvalid={!!errors.email}
          errorMessage={errors.email?.message}
          classNames={{
            input: "text-white",
            inputWrapper: "border-gray-600 hover:border-orange-500/50 focus-within:border-orange-500",
            label: "text-gray-300",
          }}
        />

        <Input
          {...register("password")}
          label="Password"
          placeholder="Enter your password"
          variant="bordered"
          isInvalid={!!errors.password}
          errorMessage={errors.password?.message}
          endContent={
            <button
              className="focus:outline-none"
              type="button"
              onClick={toggleVisibility}
            >
              {isVisible ? (
                <EyeSlashIcon className="text-2xl text-default-400 pointer-events-none" />
              ) : (
                <EyeIcon className="text-2xl text-default-400 pointer-events-none" />
              )}
            </button>
          }
          type={isVisible ? "text" : "password"}
          classNames={{
            input: "text-white",
            inputWrapper: "border-gray-600 hover:border-orange-500/50 focus-within:border-orange-500",
            label: "text-gray-300",
          }}
        />

        <Input
          {...register("confirmPassword")}
          label="Confirm Password"
          placeholder="Confirm your password"
          variant="bordered"
          isInvalid={!!errors.confirmPassword}
          errorMessage={errors.confirmPassword?.message}
          endContent={
            <button
              className="focus:outline-none"
              type="button"
              onClick={toggleConfirmVisibility}
            >
              {isConfirmVisible ? (
                <EyeSlashIcon className="text-2xl text-default-400 pointer-events-none" />
              ) : (
                <EyeIcon className="text-2xl text-default-400 pointer-events-none" />
              )}
            </button>
          }
          type={isConfirmVisible ? "text" : "password"}
          classNames={{
            input: "text-white",
            inputWrapper: "border-gray-600 hover:border-orange-500/50 focus-within:border-orange-500",
            label: "text-gray-300",
          }}
        />
      </div>

      <Button
        type="submit"
        className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-semibold shadow-lg shadow-orange-500/25"
        size="lg"
        isLoading={isLoading}
        disabled={isLoading}
      >
        {isLoading ? "Creating account..." : "Create Account"}
      </Button>

      <div className="text-center">
        <span className="text-gray-400 text-sm">
          Already have an account?{" "}
          <Link
            href="/login"
            className="text-orange-400 hover:text-orange-300 transition-colors font-medium"
          >
            Sign in
          </Link>
        </span>
      </div>
    </form>
  );
}
